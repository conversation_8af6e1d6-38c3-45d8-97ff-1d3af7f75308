<template>
    <el-dialog v-model="dialogVisible" title="申请开票" width="900px" @close="handleClose">
        <div class="invoice-dialog" v-loading="headerLoading" element-loading-text="正在加载抬头信息...">
            <!-- 供应商信息和开票金额 -->
            <div class="header-section">
                <div class="supplier-info">
                    <span class="label">供应商名称：</span>
                    <span class="value">{{ supplierName || '测试供应商' }}</span>
                </div>
                <div class="invoice-amount">
                    <span class="label">开票金额</span>
                    <span class="amount">¥{{ formatMoney(orderData.billMoney) }}</span>
                </div>
                <div class="order-date">
                    <span class="label">订单</span>
                </div>
            </div>

            <!-- 说明文字 -->
            <div class="notice-text">1. 开票金额为消费者支付金额，红包、优惠、购物券、消费返利等不可开具发票金额。2. 如开票发生错误信息、退款，开票金额出现内容错误金额时，</div>

            <!-- 开票备注 -->
            <div class="form-section">
                <div class="form-row">
                    <span class="form-label">开票备注：</span>
                    <el-input v-model="form.billingRemarks" placeholder="选填" class="remark-input" />
                </div>

                <!-- 发票类型 -->
                <div class="form-row">
                    <span class="form-label">发票类型：</span>
                    <div class="radio-group">
                        <el-radio-group v-model="form.invoiceType">
                            <el-radio label="VAT_GENERAL">增值税电子普通发票</el-radio>
                            <el-radio label="VAT_SPECIAL">增值税电子专用发票</el-radio>
                        </el-radio-group>
                    </div>
                </div>

                <!-- 抬头选择 -->
                <div class="form-row">
                    <span class="form-label">抬头选择：</span>
                    <el-select v-model="form.invoiceHeaderId" placeholder="请选择抬头" class="header-select" @change="handleHeaderChange" clearable>
                        <el-option v-for="header in invoiceHeadersList" :key="header.id" :label="header.header" :value="header.id">
                            <div class="header-option">
                                <span class="header-name">{{ header.header }}</span>
                                <span class="header-type">{{ header.invoiceHeaderType === 'ENTERPRISE' ? '企业' : '个人' }}</span>
                            </div>
                        </el-option>
                    </el-select>
                </div>

                <!-- 抬头类型 -->
                <div class="form-row">
                    <span class="form-label">抬头类型：</span>
                    <span class="form-value">{{ form.headerType === 'PERSONAL' ? '个人' : '企业' }}</span>
                    <span class="form-label ml-60">发票抬头：</span>
                    <span class="form-value">{{ getHeaderName() }}</span>
                    <span class="form-label ml-60">税号：</span>
                    <span class="form-value">{{ form.taxNo || '************' }}</span>
                </div>

                <div class="form-row">
                    <span class="form-label">开户行：</span>
                    <span class="form-value">{{ form.openingBank || '第二家店铺开户行' }}</span>
                    <span class="form-label ml-60">银行账号：</span>
                    <span class="form-value">{{ form.bankAccountNo || '第二家店铺银行账号' }}</span>
                    <span class="form-label ml-60">企业电话：</span>
                    <span class="form-value">{{ form.enterprisePhone || '***********' }}</span>
                </div>

                <div class="form-row">
                    <span class="form-label">邮箱地址：</span>
                    <span class="form-value">{{ form.email || '<EMAIL>' }}</span>
                    <span class="form-label ml-60">企业地址：</span>
                    <span class="form-value">{{ form.enterpriseAddress || '第二家店铺企业地址' }}</span>
                </div>
            </div>

            <!-- 发票详情 -->
            <div class="invoice-details">
                <div class="details-header">
                    <span class="header-title">发票详情</span>
                </div>

                <div class="details-table">
                    <div class="table-header">
                        <div class="col-order">订单号</div>
                        <div class="col-shop">开票方</div>
                        <div class="col-amount">开票金额</div>
                    </div>

                    <div class="table-body">
                        <div v-for="(order, index) in orderList" :key="index" class="table-row">
                            <div class="col-order">{{ order.orderNo }}</div>
                            <div class="col-shop">{{ order.shopName }}</div>
                            <div class="col-amount">¥{{ order.amount }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="loading">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="InvoiceDialog">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { doPostInvoiceRequest, doGetDefaultInvoiceHeader, doGetInvoiceHeaders, doPostInvoiceHeader, doGetInvoiceHeadersList } from '@/apis/invoice'

interface Props {
    visible: boolean
    orderNo: string
    orderData: any
    invoiceHeaders: any[]
    invoiceTypes?: any[]
}

interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)
const headerLoading = ref(false)
const defaultHeaderId = ref<number | null>(null)
const headerInfo = ref<any>(null)
const invoiceHeadersList = ref<any[]>([])

// 新增的数据
const supplierName = ref('测试供应商')

// 订单列表数据（模拟数据，实际应该从props.orderData中获取）
const orderList = computed(() => {
    if (props.orderData?.invoiceInfo && props.orderData.invoiceInfo.length > 0) {
        return props.orderData.invoiceInfo.map((info: any) => ({
            orderNo: info.orderNo || props.orderNo,
            shopName: info.name || '店铺名称',
            amount: formatMoney(info.billMoney || props.orderData.billMoney),
        }))
    }

    // 默认数据
    return [
        { orderNo: 'DENNINNKCIKNKNANN87892222222', shopName: '【供应商】店铺名称', amount: '25.52' },
        { orderNo: 'SK169923358878888888888888888', shopName: '【商家】店铺名称', amount: '25.52' },
        { orderNo: 'YT125987456398227812224878455', shopName: '【平台】党有灵犀', amount: '25.52' },
        { orderNo: 'YT125987456398227812224878455', shopName: '【商家】店铺名称', amount: '25.52' },
    ]
})

// 处理弹窗显示状态的计算属性
const dialogVisible = computed({
    get: () => props.visible,
    set: (value: boolean) => emit('update:visible', value),
})

// 可用的发票类型
const availableInvoiceTypes = computed(() => {
    return props.invoiceTypes || props.orderData?.invoiceTypes || ['VAT_GENERAL']
})

// 格式化金额显示
const formatMoney = (money: string | number) => {
    if (!money) return '0.00'
    const amount = typeof money === 'string' ? parseFloat(money) : money
    return (amount / 100).toFixed(2) // 假设后端返回的是分为单位
}

// 获取发票类型标签
const getInvoiceTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
        VAT_GENERAL: '增值税普通发票',
        VAT_SPECIAL: '增值税专用发票',
    }
    return labels[type] || type
}

// 表单数据
const form = reactive({
    billingRemarks: '',
    invoiceType: 'VAT_GENERAL',
    headerType: 'COMPANY', // 默认企业类型
    personalName: '',
    companyName: '第二家店铺',
    taxIdentNo: '************', // 税号
    openingBank: '第二家店铺开户行', // 开户行
    bankAccountNo: '第二家店铺银行账号', // 银行账号
    enterpriseAddress: '第二家店铺企业地址', // 企业地址
    enterprisePhone: '***********', // 企业电话
    email: '<EMAIL>',
    invoiceHeaderId: 0,
    // 保留旧字段名以兼容现有代码
    taxNo: '************',
    bankName: '第二家店铺开户行',
    bankAccount: '第二家店铺银行账号',
    companyAddress: '第二家店铺企业地址',
    companyPhone: '***********',
})

// 获取抬头名称
const getHeaderName = () => {
    if (form.headerType === 'PERSONAL') {
        return form.personalName || '个人姓名'
    } else {
        return form.companyName || '第二家店铺'
    }
}

// 表单验证规则
const rules = computed(() => ({
    billingRemarks: [{ required: true, message: '请输入开票备注', trigger: 'blur' }],
    invoiceType: [{ required: true, message: '请选择发票类型', trigger: 'change' }],
    headerType: [{ required: true, message: '请选择抬头类型', trigger: 'change' }],
    personalName: form.headerType === 'PERSONAL' ? [{ required: true, message: '请输入个人姓名', trigger: 'blur' }] : [],
    companyName: form.headerType === 'COMPANY' ? [{ required: true, message: '请输入企业名称', trigger: 'blur' }] : [],
    taxNo: form.headerType === 'COMPANY' ? [{ required: true, message: '请输入纳税人识别号', trigger: 'blur' }] : [],
}))

/**
 * 加载发票抬头列表
 */
const loadInvoiceHeadersList = async () => {
    try {
        console.log('加载发票抬头列表...')

        const { code, data, msg } = await doGetInvoiceHeadersList({
            ownerType: 'SUPPLIER',
            current: 1,
            size: 100,
        })

        if (code === 200 && data) {
            console.log('抬头列表查询成功:', data)
            invoiceHeadersList.value = data.records || data || []
        } else {
            console.log('抬头列表查询失败:', msg)
            invoiceHeadersList.value = []
        }
    } catch (error) {
        console.error('加载抬头列表失败:', error)
        invoiceHeadersList.value = []
    }
}

/**
 * 处理抬头选择变化
 */
const handleHeaderChange = async (headerId: number) => {
    if (!headerId) {
        // 清空选择
        headerInfo.value = null
        resetFormHeaderData()
        return
    }

    // 从列表中找到选中的抬头
    const selectedHeader = invoiceHeadersList.value.find((header) => header.id === headerId)
    if (selectedHeader) {
        console.log('选中抬头:', selectedHeader)
        headerInfo.value = selectedHeader
        fillFormWithHeaderInfo(selectedHeader)
    } else {
        // 如果列表中没有，则通过API查询详细信息
        await loadInvoiceHeaderInfo(headerId)
    }
}

/**
 * 重置表单抬头数据
 */
const resetFormHeaderData = () => {
    form.headerType = 'COMPANY'
    form.personalName = ''
    form.companyName = '第二家店铺'
    // 新字段名
    form.taxIdentNo = '************'
    form.openingBank = '第二家店铺开户行'
    form.bankAccountNo = '第二家店铺银行账号'
    form.enterpriseAddress = '第二家店铺企业地址'
    form.enterprisePhone = '***********'
    form.email = '<EMAIL>'
    // 旧字段名（保持兼容性）
    form.taxNo = '************'
    form.bankName = '第二家店铺开户行'
    form.bankAccount = '第二家店铺银行账号'
    form.companyAddress = '第二家店铺企业地址'
    form.companyPhone = '***********'
    form.invoiceHeaderId = 0
}

/**
 * 查询默认发票抬头
 */
const loadDefaultInvoiceHeader = async () => {
    try {
        headerLoading.value = true
        console.log('查询默认发票抬头...')

        const { code, data, msg } = await doGetDefaultInvoiceHeader()

        if (code === 200 && data) {
            console.log('默认抬头查询成功:', data)
            defaultHeaderId.value = data.id || data.headerId

            // 如果有默认抬头ID，查询详细信息
            if (defaultHeaderId.value) {
                await loadInvoiceHeaderInfo(defaultHeaderId.value)
            }
        } else {
            console.log('没有默认抬头或查询失败:', msg)
            // 没有默认抬头，使用空表单
            defaultHeaderId.value = null
            headerInfo.value = null
        }
    } catch (error) {
        console.error('查询默认抬头失败:', error)
        defaultHeaderId.value = null
        headerInfo.value = null
    } finally {
        headerLoading.value = false
    }
}

/**
 * 查询抬头详细信息
 */
const loadInvoiceHeaderInfo = async (headerId: number) => {
    try {
        console.log('查询抬头详细信息, ID:', headerId)

        const { code, data, msg } = await doGetInvoiceHeaders(headerId.toString())

        if (code === 200 && data) {
            console.log('抬头信息查询成功:', data)
            headerInfo.value = data

            // 预填表单数据
            fillFormWithHeaderInfo(data)
        } else {
            console.error('抬头信息查询失败:', msg)
        }
    } catch (error) {
        console.error('查询抬头信息失败:', error)
    }
}

/**
 * 用抬头信息填充表单
 */
const fillFormWithHeaderInfo = (headerData: any) => {
    if (!headerData) return

    console.log('填充表单数据:', headerData)

    // 设置抬头类型 - 根据API字段映射
    form.headerType = headerData.invoiceHeaderType === 'ENTERPRISE' ? 'COMPANY' : 'PERSONAL'

    if (headerData.invoiceHeaderType === 'PERSONAL') {
        form.personalName = headerData.header || ''
    } else {
        form.companyName = headerData.header || '第二家店铺'
        // 使用新字段名
        form.taxIdentNo = headerData.taxIdentNo || '************'
        form.openingBank = headerData.openingBank || '第二家店铺开户行'
        form.bankAccountNo = headerData.bankAccountNo || '第二家店铺银行账号'
        form.enterpriseAddress = headerData.enterpriseAddress || '第二家店铺企业地址'
        form.enterprisePhone = headerData.enterprisePhone || '***********'
        form.email = headerData.email || '<EMAIL>'

        // 同时更新旧字段名以保持兼容性
        form.taxNo = headerData.taxIdentNo || '************'
        form.bankName = headerData.openingBank || '第二家店铺开户行'
        form.bankAccount = headerData.bankAccountNo || '第二家店铺银行账号'
        form.companyAddress = headerData.enterpriseAddress || '第二家店铺企业地址'
        form.companyPhone = headerData.enterprisePhone || '***********'
    }

    // 设置抬头ID
    form.invoiceHeaderId = headerData.id || 0
}

/**
 * 创建新的发票抬头
 */
const createInvoiceHeader = async () => {
    try {
        const headerData = {
            invoiceHeaderType: form.headerType === 'COMPANY' ? 'ENTERPRISE' : 'PERSONAL',
            header: form.headerType === 'PERSONAL' ? form.personalName : form.companyName,
            taxIdentNo: form.headerType === 'COMPANY' ? form.taxIdentNo : undefined,
            openingBank: form.headerType === 'COMPANY' ? form.openingBank : undefined,
            bankAccountNo: form.headerType === 'COMPANY' ? form.bankAccountNo : undefined,
            enterpriseAddress: form.headerType === 'COMPANY' ? form.enterpriseAddress : undefined,
            enterprisePhone: form.headerType === 'COMPANY' ? form.enterprisePhone : undefined,
            email: form.email,
            isDefault: !defaultHeaderId.value, // 如果没有默认抬头，设为默认
        }

        console.log('创建发票抬头:', headerData)

        const { code, data, msg } = await doPostInvoiceHeader(headerData)

        if (code === 200 && data) {
            console.log('抬头创建成功:', data)
            form.invoiceHeaderId = data.id || data.headerId
            return data.id || data.headerId
        } else {
            console.error('抬头创建失败:', msg)
            throw new Error(msg || '抬头创建失败')
        }
    } catch (error) {
        console.error('创建抬头失败:', error)
        throw error
    }
}

// 处理抬头类型变化
const handleHeaderTypeChange = () => {
    // 清空相关字段
    if (form.headerType === 'PERSONAL') {
        form.companyName = ''
        form.taxNo = ''
        form.bankName = ''
        form.bankAccount = ''
        form.companyAddress = ''
        form.companyPhone = ''
    } else {
        form.personalName = ''
    }

    // 重置抬头ID
    form.invoiceHeaderId = 0
}

// 关闭弹窗
const handleClose = () => {
    emit('update:visible', false)
    // 重置表单
    formRef.value?.resetFields()
}

// 提交申请
const handleSubmit = async () => {
    if (!formRef.value) return

    try {
        await formRef.value.validate()
        loading.value = true

        // 确保有有效的抬头ID
        let headerId = form.invoiceHeaderId

        // 如果没有抬头ID，需要先创建抬头
        if (!headerId || headerId === 0) {
            console.log('没有抬头ID，创建新抬头...')
            headerId = await createInvoiceHeader()
        }

        const requestData = {
            invoiceOwnerType: 'SUPPLIER',
            orderNos: props.orderData?.orderNos || [props.orderNo],
            invoiceType: form.invoiceType,
            billingRemarks: form.billingRemarks,
            invoiceHeaderId: headerId,
        }

        console.log('提交发票申请:', requestData)

        const { code, msg } = await doPostInvoiceRequest(requestData)

        if (code === 200) {
            ElMessage.success('发票申请提交成功')
            emit('success')
            handleClose()
        } else {
            ElMessage.error(msg || '发票申请提交失败')
        }
    } catch (error) {
        console.error('发票申请失败:', error)
        ElMessage.error('发票申请提交失败，请稍后重试')
    } finally {
        loading.value = false
    }
}

// 监听visible变化
watch(
    () => props.visible,
    async (newVal) => {
        if (newVal) {
            // 弹窗打开时，预填一些数据
            console.log('发票弹窗数据:', props.orderData)
            console.log('可用发票类型:', availableInvoiceTypes.value)

            // 预设第一个可用的发票类型
            if (availableInvoiceTypes.value.length > 0) {
                form.invoiceType = availableInvoiceTypes.value[0]
            }

            // 先加载抬头列表
            await loadInvoiceHeadersList()

            // 然后尝试加载默认抬头信息
            await loadDefaultInvoiceHeader()

            // 如果没有默认抬头，但有订单中的发票信息，则使用订单信息
            if (!headerInfo.value && props.orderData?.invoiceInfo && props.orderData.invoiceInfo.length > 0) {
                const invoiceInfo = props.orderData.invoiceInfo[0]
                console.log('使用订单中的发票信息:', invoiceInfo)

                if (invoiceInfo.invoiceToType === 'PERSONAL') {
                    form.headerType = 'PERSONAL'
                    form.personalName = invoiceInfo.name
                } else {
                    form.headerType = 'COMPANY'
                    form.companyName = invoiceInfo.name || '第二家店铺'
                }
            }

            // 如果有供应商信息，更新供应商名称
            if (props.orderData?.supplierName) {
                supplierName.value = props.orderData.supplierName
            }
        }
    },
)
</script>

<style scoped lang="scss">
.invoice-dialog {
    font-size: 14px;
    color: #333;

    .header-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #eee;

        .supplier-info {
            .label {
                color: #666;
                margin-right: 8px;
            }
            .value {
                color: #333;
                font-weight: 500;
            }
        }

        .invoice-amount {
            text-align: center;

            .label {
                display: block;
                color: #666;
                font-size: 14px;
                margin-bottom: 4px;
            }

            .amount {
                color: #ff4d4f;
                font-size: 20px;
                font-weight: bold;
            }
        }

        .order-date {
            .label {
                color: #666;
            }
        }
    }

    .notice-text {
        color: #666;
        font-size: 12px;
        line-height: 1.5;
        margin-bottom: 20px;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 4px;
    }

    .form-section {
        margin-bottom: 24px;

        .form-row {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            min-height: 32px;

            .form-label {
                color: #333;
                min-width: 80px;
                margin-right: 12px;
                font-weight: 500;

                &.ml-60 {
                    margin-left: 60px;
                }
            }

            .form-value {
                color: #666;
                margin-right: 20px;
            }

            .remark-input {
                width: 300px;
            }

            .header-select {
                width: 200px;
            }

            .header-option {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .header-name {
                    flex: 1;
                    color: #333;
                }

                .header-type {
                    color: #999;
                    font-size: 12px;
                    margin-left: 8px;
                }
            }

            .radio-group {
                :deep(.el-radio) {
                    margin-right: 24px;

                    .el-radio__label {
                        color: #333;
                    }
                }
            }
        }
    }

    .invoice-details {
        .details-header {
            margin-bottom: 12px;

            .header-title {
                color: #333;
                font-weight: 500;
                font-size: 16px;
            }
        }

        .details-table {
            border: 1px solid #e4e7ed;
            border-radius: 4px;

            .table-header {
                display: flex;
                background: #f5f7fa;
                border-bottom: 1px solid #e4e7ed;
                font-weight: 500;
                color: #333;

                > div {
                    padding: 12px 16px;
                    border-right: 1px solid #e4e7ed;

                    &:last-child {
                        border-right: none;
                    }
                }
            }

            .table-body {
                .table-row {
                    display: flex;
                    border-bottom: 1px solid #e4e7ed;

                    &:last-child {
                        border-bottom: none;
                    }

                    > div {
                        padding: 12px 16px;
                        border-right: 1px solid #e4e7ed;
                        color: #666;

                        &:last-child {
                            border-right: none;
                        }
                    }
                }
            }

            .col-order {
                flex: 2;
                min-width: 200px;
            }

            .col-shop {
                flex: 1;
                min-width: 150px;
            }

            .col-amount {
                flex: 1;
                min-width: 100px;
                text-align: right;
            }
        }
    }
}

.dialog-footer {
    text-align: right;
    padding-top: 16px;
    border-top: 1px solid #eee;

    .el-button {
        min-width: 80px;
    }
}

// Element Plus 样式覆盖
:deep(.el-dialog__header) {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #eee;
}

:deep(.el-dialog__body) {
    padding: 24px;
}

:deep(.el-dialog__footer) {
    padding: 16px 24px 20px;
}
</style>
